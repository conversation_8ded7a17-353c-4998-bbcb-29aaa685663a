
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - VIVEZ</title>
    <link rel="stylesheet" href="/style.css">
    <link rel="stylesheet" href="checkout.css">
    <script src="https://www.paypal.com/sdk/js?client-id=BAAmCpbSlR9fAtgUw9GZm83VZ5fRXW-QpU9btcUdOIjlzZ9joX8tHJkaLdmKPV33Znp4pLN74Q5LusuN7g&currency=CHF&enable-funding=card&disable-funding=venmo"></script>
</head>
<body>
    <nav class="main-nav">
        <div class="nav-center">
            <a href="/">
                <img src="/assets/images/logo/logo vivez 250603.png" alt="VIVEZ Logo" class="nav-logo">
            </a>
        </div>
    </nav>

    <div class="checkout-container">
        <main class="checkout-main">
            <div class="checkout-steps">
                <div class="step active">Information</div>
                <div class="step-divider"></div>
                <div class="step">Shipping</div>
                <div class="step-divider"></div>
                <div class="step">Payment</div>
            </div>

            <section class="express-checkout">
                <h2 class="section-title">Express checkout</h2>
                <div class="express-payment-grid">
                    <button class="express-btn" id="express-credit-card">
                        <span class="credit-card-icon">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"/>
                            </svg>
                        </span>
                        <span>Credit Card</span>
                    </button>
                    <div class="paypal-express" id="paypal-button-container"></div>
                </div>
            </section>

            <div class="separator"><span>OR</span></div>

            <form class="checkout-form" id="checkout-form">
                <section class="form-section">
                    <h2 class="section-title">Contact</h2>
                    <div class="field">
                        <input type="email" name="email" placeholder="Email" class="field__input" id="contact-email" required>
                    </div>
                    <div class="field__checkbox">
                        <input type="checkbox" id="newsletter">
                        <label for="newsletter">Email me with news and offers</label>
                    </div>
                </section>

                <section class="form-section">
                    <h2 class="section-title">Delivery method</h2>
                    <div class="delivery-method-selection">
                        <label class="delivery-method-option">
                            <input type="radio" name="delivery-type" value="home" checked onchange="toggleDeliveryForms()">
                            <div class="delivery-method-content">
                                <div class="delivery-method-info">
                                    <span class="delivery-method-name">Home delivery</span>
                                    <span class="delivery-method-desc">Delivered to your address</span>
                                </div>
                                <span class="delivery-method-price">CHF 5.90</span>
                            </div>
                        </label>
                        <label class="delivery-method-option">
                            <input type="radio" name="delivery-type" value="pickup" onchange="toggleDeliveryForms()">
                            <div class="delivery-method-content">
                                <div class="delivery-method-info">
                                    <span class="delivery-method-name">Pickup point</span>
                                    <span class="delivery-method-desc">Pickup at collection point</span>
                                </div>
                                <span class="delivery-method-price">CHF 3.90</span>
                            </div>
                        </label>
                    </div>
                </section>

                <section class="form-section" id="home-delivery-section">
                    <h2 class="section-title">Shipping address</h2>
                    <div class="field">
                        <select name="country" class="field__input" id="country" required>
                            <option value="">Select country</option>
                        </select>
                    </div>
                    <div class="field--half">
                        <div class="field">
                            <input type="text" name="firstName" placeholder="First name" class="field__input" id="first-name" required>
                        </div>
                        <div class="field">
                            <input type="text" name="lastName" placeholder="Last name" class="field__input" id="last-name" required>
                        </div>
                    </div>
                    <div class="field">
                        <input type="text" name="company" placeholder="Company (optional)" class="field__input" id="company">
                    </div>
                    <div class="field">
                        <input type="text" name="address" placeholder="Address" class="field__input" id="address" required>
                    </div>
                    <div class="field">
                        <input type="text" name="apartment" placeholder="Apartment, suite, etc. (optional)" class="field__input" id="address2">
                    </div>
                    <div class="field--half">
                        <div class="field">
                            <input type="text" name="postalCode" placeholder="Postal code" class="field__input" id="postal-code" required>
                        </div>
                        <div class="field">
                            <input type="text" name="city" placeholder="City" class="field__input" id="city" required>
                        </div>
                    </div>
                    <div class="field">
                        <input type="tel" name="phone" placeholder="Phone" class="field__input" id="phone" required>
                    </div>
                </section>

                <section class="form-section" id="pickup-point-section" style="display: none;">
                    <h2 class="section-title">Pickup point selection</h2>
                    <div class="pickup-point-search">
                        <div class="field">
                            <input type="text" name="pickup-search" placeholder="Enter your postal code or city" class="field__input" id="pickup-search">
                            <button type="button" class="search-btn" onclick="searchPickupPoints()">Search</button>
                        </div>
                    </div>
                    <div class="pickup-points-list" id="pickup-points-list">
                        <p class="pickup-info">Enter your location to find nearby pickup points</p>
                    </div>
                </section>

                <section class="form-section">
                    <h2 class="section-title">Payment</h2>
                    <p class="section-subtitle">All transactions are secure and encrypted.</p>

                    <div class="payment-methods">
                        <div class="payment-method">
                            <label class="payment-option">
                                <input type="radio" name="payment" value="card" checked>
                                <div class="payment-content">
                                    <span class="payment-name">Credit card</span>
                                    <div class="payment-icons">
                                        <svg width="32" height="20" viewBox="0 0 32 20" fill="none" class="payment-icon visa">
                                            <rect width="32" height="20" rx="4" fill="#1A1F71"/>
                                            <path d="M11.5 7.5L10 12.5H8.5L7.5 8.5C7.4 8.1 7.2 7.9 6.9 7.8C6.4 7.6 5.8 7.4 5.2 7.2L5.3 7H7.8C8.2 7 8.5 7.3 8.6 7.7L9.2 10.8L10.7 7H12.2L11.5 7.5ZM16.8 12.5H15.3L16.3 7H17.8L16.8 12.5ZM21.2 8.8C21.2 8.4 20.9 8.1 20.4 8.1C19.8 8.1 19.1 8.3 18.5 8.6L18.8 7.4C19.4 7.1 20.1 7 20.7 7C21.9 7 22.7 7.6 22.7 8.6C22.7 9.4 22.1 9.8 21.4 10.1C20.9 10.3 20.6 10.5 20.6 10.8C20.6 11 20.8 11.2 21.2 11.2C21.7 11.2 22.2 11.1 22.6 10.9L22.3 12.1C21.9 12.3 21.4 12.4 20.9 12.4C19.6 12.4 18.8 11.8 18.8 10.9C18.8 9.6 20.6 9.5 20.6 8.8H21.2ZM26.5 12.5H25.2L25.8 7H27.5L26.5 12.5Z" fill="white"/>
                                        </svg>
                                        <svg width="32" height="20" viewBox="0 0 32 20" fill="none" class="payment-icon mastercard">
                                            <rect width="32" height="20" rx="4" fill="#EB001B"/>
                                            <circle cx="12" cy="10" r="6" fill="#EB001B"/>
                                            <circle cx="20" cy="10" r="6" fill="#FF5F00"/>
                                            <circle cx="16" cy="10" r="6" fill="#F79E1B"/>
                                        </svg>
                                        <svg width="32" height="20" viewBox="0 0 32 20" fill="none" class="payment-icon amex">
                                            <rect width="32" height="20" rx="4" fill="#006FCF"/>
                                            <path d="M8.5 7H10L11 9L12 7H13.5L12 10L13.5 13H12L11 11L10 13H8.5L10 10L8.5 7ZM15 7H19V8H16V9H18.5V10H16V11H19V12H15V7ZM20.5 7H22L23.5 10.5L25 7H26.5V13H25V9L23.8 12H23.2L22 9V13H20.5V7Z" fill="white"/>
                                        </svg>
                                        <svg width="32" height="20" viewBox="0 0 32 20" fill="none" class="payment-icon cb">
                                            <rect width="32" height="20" rx="4" fill="#0055A4"/>
                                            <path d="M8 7H12V8H9V9H11V10H9V11H12V12H8V7ZM13 7H17V8H14V9H16V10H14V11H17V12H13V7ZM18 7H19V12H18V7ZM20 7H24V8H21V9H23V10H21V11H24V12H20V7Z" fill="white"/>
                                        </svg>
                                        <span class="more-options">+1</span>
                                    </div>
                                </div>
                            </label>

                            <div class="card-form" id="card-form">
                                <div class="field">
                                    <input type="text" name="cardNumber" placeholder="Card number" class="field__input card-number-input" maxlength="19" required>
                                    <div class="card-number-icons">
                                        <svg width="20" height="12" viewBox="0 0 32 20" fill="none" class="card-icon">
                                            <rect width="32" height="20" rx="4" fill="#1A1F71"/>
                                            <path d="M11.5 7.5L10 12.5H8.5L7.5 8.5C7.4 8.1 7.2 7.9 6.9 7.8C6.4 7.6 5.8 7.4 5.2 7.2L5.3 7H7.8C8.2 7 8.5 7.3 8.6 7.7L9.2 10.8L10.7 7H12.2L11.5 7.5ZM16.8 12.5H15.3L16.3 7H17.8L16.8 12.5ZM21.2 8.8C21.2 8.4 20.9 8.1 20.4 8.1C19.8 8.1 19.1 8.3 18.5 8.6L18.8 7.4C19.4 7.1 20.1 7 20.7 7C21.9 7 22.7 7.6 22.7 8.6C22.7 9.4 22.1 9.8 21.4 10.1C20.9 10.3 20.6 10.5 20.6 10.8C20.6 11 20.8 11.2 21.2 11.2C21.7 11.2 22.2 11.1 22.6 10.9L22.3 12.1C21.9 12.3 21.4 12.4 20.9 12.4C19.6 12.4 18.8 11.8 18.8 10.9C18.8 9.6 20.6 9.5 20.6 8.8H21.2ZM26.5 12.5H25.2L25.8 7H27.5L26.5 12.5Z" fill="white"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="field--half">
                                    <div class="field">
                                        <input type="text" name="expiry" placeholder="Expiration date (MM / YY)" class="field__input" maxlength="7" required>
                                    </div>
                                    <div class="field security-code-field">
                                        <input type="text" name="cvv" placeholder="Security code" class="field__input" maxlength="4" required>
                                        <button type="button" class="security-info-btn" onclick="showSecurityInfo()">
                                            <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="field">
                                    <input type="text" name="cardName" placeholder="Name on card" class="field__input" required>
                                </div>
                                <div class="field__checkbox">
                                    <input type="checkbox" id="use-shipping-address" checked>
                                    <label for="use-shipping-address">Use shipping address as billing address</label>
                                </div>
                            </div>
                        </div>

                        <div class="payment-method" id="twint-method" style="display: none;">
                            <label class="payment-option">
                                <input type="radio" name="payment" value="twint">
                                <div class="payment-content">
                                    <span class="payment-name">TWINT</span>
                                    <img src="https://www.postfinance.ch/content/dam/pfch/image/about/media/logo_twint_new_big.png" alt="TWINT" class="payment-logo">
                                </div>
                            </label>
                        </div>

                        <div class="payment-method" id="postfinance-method" style="display: none;">
                            <label class="payment-option">
                                <input type="radio" name="payment" value="postfinance">
                                <div class="payment-content">
                                    <span class="payment-name">PostFinance</span>
                                    <img src="https://www.postfinance.ch/content/dam/pfch/image/about/media/logo_pf_big.png" alt="PostFinance" class="payment-logo">
                                </div>
                            </label>
                        </div>
                    </div>
                </section>

                <div class="form-footer">
                    <a href="/shop/shop.html" class="return-link">← Back to shop</a>
                    <button type="submit" class="continue-btn">Complete order</button>
                </div>
            </form>
        </main>

        <aside class="order-summary">
            <div class="order-summary-header">
                <h2 class="summary-title">Order summary</h2>
                <button class="summary-toggle">Show order summary</button>
            </div>
            <div class="order-summary-content">
                <div class="order-items" id="order-items">
                    </div>
                <div class="promo-code-section">
                    <div class="promo-code-field">
                        <input type="text" placeholder="Discount code" class="field__input">
                        <button class="promo-apply-btn">Apply</button>
                    </div>
                </div>
                <div class="summary-totals">
                    <div class="summary-line">
                        <span>Subtotal · <span id="article-count">0</span> articles</span>
                        <span id="summary-subtotal">CHF 0.00</span>
                    </div>
                    <div class="summary-line">
                        <span>Shipping
                            <button class="info-tooltip-btn" onclick="showShippingInfo()">
                                <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </span>
                        <span id="shipping-cost">Free</span>
                    </div>
                    <div class="summary-line total">
                        <span>Total</span>
                        <span id="summary-total">CHF 0.00</span>
                    </div>
                </div>
            </div>
        </aside>
    </div> 
    <footer class="footer"> 
            <div class="footer-divider"></div> 
            <div class="footer-social"> 
                <ul class="social-list"> 
                    <li class="social-item"> 
                        <a href="facebook.com/vivez" class="social-link"> 
                            <span class="social-icon-wrapper"> 
                                <svg class="social-icon" viewBox="0 0 20 20"> 
                                    <path fill="currentColor" d="M18 10.049C18 5.603 14.419 2 10 2s-8 3.603-8 8.049C2 14.067 4.925 17.396 8.75 18v-5.624H6.719v-2.328h2.03V8.275c0-2.017 1.195-3.132 3.023-3.132.874 0 1.79.158 1.79.158v1.98h-1.009c-.994 0-1.303.621-1.303 1.258v1.51h2.219l-.355 2.326H11.25V18c3.825-.604 6.75-3.933 6.75-7.951"/> 
                                </svg> 
                            </span> 
                        </a> 
                    </li> 
                    <li class="social-item"> 
                        <a href="instagram.com/vivez" class="social-link"> 
                            <span class="social-icon-wrapper"> 
                                <svg class="social-icon" viewBox="0 0 20 20"> 
                                    <path fill="currentColor" d="M13.23 3.492c-.84-.037-1.096-.046-3.23-.046-2.144 0-2.39.01-3.238.055-.776.027-1.195.164-1.487.273a2.4 2.4 0 0 0-.912.593 2.5 2.5 0 0 0-.602.922c-.11.282-.238.702-.274 1.486-.046.84-.046 1.095-.046 3.23s.01 2.39.046 3.229c.004.51.097 1.016.274 1.495.145.365.319.639.602.913.282.282.538.456.92.602.474.176.974.268 1.479.273.848.046 1.103.046 3.238.046s2.39-.01 3.23-.046c.784-.036 1.203-.164 1.486-.273.374-.146.648-.329.921-.602.283-.283.447-.548.602-.922.177-.476.27-.979.274-1.486.037-.84.046-1.095.046-3.23s-.01-2.39-.055-3.229c-.027-.784-.164-1.204-.274-1.495a2.4 2.4 0 0 0-.593-.913 2.6 2.6 0 0 0-.92-.602c-.284-.11-.703-.237-1.488-.273ZM6.697 2.05c.857-.036 1.131-.045 3.302-.045 2.171 0 2.445.009 3.302.045.664.014 1.321.14 1.943.374a4 4 0 0 1 1.414.922c.41.397.728.88.93 1.414.23.622.354 1.279.365 1.942C18 7.56 18 7.824 18 10.005c0 2.17-.01 2.444-.046 3.292-.036.858-.173 1.442-.374 1.943-.2.53-.474.976-.92 1.423a3.9 3.9 0 0 1-1.415.922c-.51.191-1.095.337-1.943.374-.857.036-1.122.045-3.302.045-2.171 0-2.445-.009-3.302-.055-.849-.027-1.432-.164-1.943-.364a4.15 4.15 0 0 1-1.414-.922 4.1 4.1 0 0 1-.93-1.423c-.183-.51-.329-1.085-.365-1.943C2.009 12.45 2 12.167 2 10.004c0-2.161 0-2.435.055-3.302.027-.848.164-1.432.365-1.942a4.4 4.4 0 0 1 .92-1.414 4.2 4.2 0 0 1 1.415-.93c.51-.183 1.094-.33 1.943-.366Zm.427 4.806a4.105 4.105 0 1 1 5.805 5.805 4.105 4.105 0 0 1-5.805-5.805m1.882 5.371a2.668 2.668 0 1 0 2.042-4.93 2.668 2.668 0 0 0-2.042 4.93m5.922-5.942a.958.958 0 1 1-1.355-1.355.958.958 0 0 1 1.355 1.355"/> 
                                </svg> 
                            </span> 
                        </a> 
                    </li> 
                    <li class="social-item"> 
                        <a href="tiktok.com/vivez" class="social-link"> 
                            <span class="social-icon-wrapper"> 
                                <svg class="social-icon" viewBox="0 0 20 20"> 
                                    <path fill="currentColor" d="M10.511 1.705h2.74s-.157 3.51 3.795 3.768v2.711s-2.114.129-3.796-1.158l.028 5.606A5.073 5.073 0 1 1 8.213 7.56h.708v2.785a2.298 2.298 0 1 0 1.618 2.205z"/> 
                                </svg> 
                            </span> 
                        </a> 
                    </li> 
                    <li class="social-item"> 
                        <a href="pinterest.com/vivez" class="social-link"> 
                            <span class="social-icon-wrapper"> 
                                <svg class="social-icon" viewBox="0 0 20 20"> 
                                    <path fill="currentColor" d="M10 2.01a8.1 8.1 0 0 1 5.666 2.353 8.09 8.09 0 0 1 1.277 9.68A7.95 7.95 0 0 1 10 18.04a8.2 8.2 0 0 1-2.276-.307c.403-.653.672-1.24.816-1.729l.567-2.2c.134.27.393.5.768.702.384.192.768.297 1.19.297q1.254 0 2.248-.72a4.7 4.7 0 0 0 1.537-1.969c.37-.89.554-1.848.537-2.813 0-1.249-.48-2.315-1.43-3.227a5.06 5.06 0 0 0-3.65-1.374c-.893 0-1.729.154-2.478.461a5.02 5.02 0 0 0-3.236 4.552c0 .72.134 1.355.413 1.902.269.538.672.922 1.22 1.152.096.039.182.039.25 0 .066-.028.114-.096.143-.192l.173-.653c.048-.144.02-.288-.105-.432a2.26 2.26 0 0 1-.548-1.565 3.803 3.803 0 0 1 3.976-3.861c1.047 0 1.863.288 2.44.855.585.576.883 1.315.883 2.228a6.8 6.8 0 0 1-.317 2.122 3.8 3.8 0 0 1-.893 1.556c-.384.384-.836.576-1.345.576-.413 0-.749-.144-1.018-.451-.259-.307-.345-.672-.25-1.085q.22-.77.452-1.537l.173-.701c.057-.25.086-.451.086-.624 0-.346-.096-.634-.269-.855-.192-.22-.451-.336-.797-.336-.432 0-.797.192-1.085.595-.288.394-.442.893-.442 1.499.005.374.063.746.173 1.104l.058.144c-.576 2.478-.913 3.938-1.037 4.36-.116.528-.154 1.153-.125 1.863A8.07 8.07 0 0 1 2 10.03c0-2.208.778-4.11 2.343-5.666A7.72 7.72 0 0 1 10 2.001z"/> 
                                </svg> 
                            </span> 
                        </a> 
                    </li> 
                </ul> 
            </div> 
        <div class="footer-bottom">
            <div class="footer-copyright">
                <small>© 2025, Vivez</small>
                <div class="footer-policies">
                    <a href="/policies/privacy-policy">Privacy policy</a>
                    <a href="/policies/refund-policy">Refund policy</a>
                    <a href="/policies/shipping-policy">Shipping policy</a>
                    <a href="/policies/terms-of-service">Terms of service</a>
                </div>
            </div>
        </div>
        
    </footer>

    <!-- Shipping Information Modal -->
    <div id="shipping-info-modal" class="modal-overlay" onclick="hideShippingInfo()">
        <div class="modal-content" onclick="event.stopPropagation()">
            <div class="modal-header">
                <h3>Shipping Policy</h3>
                <button class="modal-close" onclick="hideShippingInfo()">×</button>
            </div>
            <div class="modal-body">
                <div class="policy-section">
                    <h4>1. Order Processing</h4>
                    <p>Orders are processed and shipped within 1-3 business days (Monday through Friday, excluding holidays). Once your order is shipped, you will receive a confirmation email with tracking information.</p>
                </div>

                <div class="policy-section">
                    <h4>2. Processing Times</h4>
                    <p>Orders are prepared and shipped within 1-3 business days (Monday through Friday, excluding holidays). Once your order is shipped, you will receive a confirmation email with tracking number.</p>
                </div>

                <div class="policy-section">
                    <h4>3. Estimated Delivery Times</h4>
                    <ul>
                        <li><strong>Switzerland:</strong> 1-3 business days</li>
                        <li><strong>Europe:</strong> 3-7 business days</li>
                        <li><strong>International:</strong> 7-14 business days</li>
                    </ul>
                    <p>These times may vary depending on your location, shipping carrier, and holiday periods.</p>
                </div>

                <div class="policy-section">
                    <h4>4. Shipping Costs</h4>
                    <p>Shipping costs are automatically calculated during checkout based on your destination and selected shipping method. We offer free shipping on orders over CHF 50 within Switzerland.</p>
                </div>

                <div class="policy-section">
                    <h4>5. Order Tracking</h4>
                    <p>Once your order ships, you will receive an email with tracking information. You can track your order at any time using the provided tracking link.</p>
                </div>

                <div class="policy-section">
                    <h4>6. Delivery Issues</h4>
                    <p>If you experience any delivery issues or if your package is lost or damaged during shipping, please contact us immediately and we will work to resolve the issue.</p>
                </div>

                <div class="policy-section">
                    <h4>7. Incorrect or Incomplete Address</h4>
                    <p>Please ensure your shipping address is correct and complete. In case of return due to incorrect address, additional shipping fees may apply.</p>
                </div>

                <div class="policy-section">
                    <h4>8. Customs and Duties (International Orders)</h4>
                    <p>For international orders outside the European Union, customs duties and import taxes may apply upon arrival in your country. These fees are the responsibility of the customer.</p>
                </div>

                <div class="policy-section">
                    <h4>9. Questions</h4>
                    <p>For any questions regarding shipping, please contact us at:</p>
                    <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p><strong>Or via DM on Instagram:</strong> <a href="https://instagram.com/vivez" target="_blank">@vivez</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Security Code Information Modal -->
    <div id="security-info-modal" class="modal-overlay" onclick="hideSecurityInfo()">
        <div class="modal-content security-modal" onclick="event.stopPropagation()">
            <div class="modal-header">
                <h3>Security code</h3>
                <button class="modal-close" onclick="hideSecurityInfo()">×</button>
            </div>
            <div class="modal-body">
                <div class="security-explanation">
                    <p><strong>3-digit security code</strong> generally located on the back of your card. The security code for American Express cards is composed of 4 digits and is located on the front of your card.</p>

                    <div class="card-examples">
                        <div class="card-example">
                            <div class="card-visual visa-card">
                                <div class="card-brand">VISA</div>
                                <div class="card-number">•••• •••• •••• 1234</div>
                                <div class="card-holder">CARDHOLDER NAME</div>
                                <div class="card-back">
                                    <div class="magnetic-stripe"></div>
                                    <div class="signature-panel">
                                        <span class="cvv-highlight">123</span>
                                    </div>
                                </div>
                            </div>
                            <p class="card-description">For Visa, Mastercard, and other cards: <strong>3 digits on the back</strong></p>
                        </div>

                        <div class="card-example">
                            <div class="card-visual amex-card">
                                <div class="card-brand">AMERICAN EXPRESS</div>
                                <div class="card-number">•••• •••••• •1234</div>
                                <div class="card-holder">CARDHOLDER NAME</div>
                                <div class="amex-cvv">1234</div>
                            </div>
                            <p class="card-description">For American Express: <strong>4 digits on the front</strong></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/Products/products.js"></script>
    <script src="/script.js"></script>
    <script src="/Checkout/checkout.js"></script>
</body>
</html>