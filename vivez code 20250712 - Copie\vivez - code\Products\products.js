const products = [
    {
        id: "reflective-knit",
        name: "Reflective Knit",
        price: 200,
        mainImage: "/assets/images/produits-test/2501-DVRL-REFLECTIVEKNIT-off_fond.jpg",
        description: "Your product description here",
        additionalImages: [
            "/assets/images/produits-test/image2.jpg",
            "/assets/images/produits-test/image3.jpg"
        ]
    },
    {
        id: "blue-dragon-knit",
        name: "Blue Dragon Knit",
        price: 200,
        mainImage: "/assetsassets/images/produits-test/blue-dragon.jpg",
        description: "Your product description here",
        additionalImages: [
            "/assets/images/produits-test/image2.jpg",
            "/assets/images/produits-test/image3.jpg"
        ]
    },
    {
        id: "black-shirt",
        name: "Black Shirt",
        price: 200,
        mainImage: "/assets/images/produits-test/black-2_1.jpg",
        description: "Your product description here",
        additionalImages: [
            "/assets/images/produits-test/image2.jpg",
            "/assets/images/produits-test/image3.jpg"
        ]
    }
];

// Cart Logic
let cart = JSON.parse(localStorage.getItem('cart') || "[]");

function saveCart() {
    localStorage.setItem('cart', JSON.stringify(cart));
}

function addToCart(productId, size) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    const existingItem = cart.find(item => item.id === productId && item.size === size);

    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: productId,
            name: product.name,
            price: product.price,
            size: size,
            image: product.mainImage,
            quantity: 1
        });
    }

    saveCart();
    updateCartUI(); // ✅ Call this to refresh UI
}

function updateCartUI() {
    const cartContent = document.querySelector('.cart-content');
    cart = JSON.parse(localStorage.getItem('cart')) || [];

    // ✅ Update the cart icon counter
    const cartCount = document.querySelector('.cart-count');
    if (cartCount) {
        cartCount.textContent = cart.reduce((total, item) => total + item.quantity, 0);
    }

    if (cart.length === 0) {
        cartContent.innerHTML = `<div class="empty-cart-container">
            <p class="empty-cart">Your cart is empty.</p>
            <p class="empty-cart-message">Looks like you haven't added anything to your cart yet.</p>
            <a href="#" class="start-shopping-btn">Start Shopping</a>
        </div>`;
    } else {
        cartContent.innerHTML = `<div class="cart-items">
            ${cart.map(item => `
                <div class="cart-item">
                    <img src="${item.image}" alt="${item.name}" class="cart-item-image">
                    <div class="cart-item-details">
                        <h3>${item.name}</h3>
                        <p>Size: ${item.size}</p>
                        <p>Quantity: ${item.quantity}</p>
                        <p>CHF ${item.price}</p>
                    </div>
                    <button class="remove-item" data-id="${item.id}" data-size="${item.size}">×</button>
                </div>
            `).join('')}
            <div class="cart-total">
                <p>Total: CHF ${cart.reduce((total, item) => total + (item.price * item.quantity), 0)}</p>
                <button class="checkout-btn">Checkout</button>
            </div>
        </div>`;

        document.querySelectorAll('.remove-item').forEach(button => {
            button.addEventListener('click', () => {
                const { id, size } = button.dataset;
                cart = cart.filter(item => !(item.id === id && item.size === size));
                saveCart();
                updateCartUI();
            });
        });
    }
}

document.addEventListener('DOMContentLoaded', () => {
    updateCartUI();
});

