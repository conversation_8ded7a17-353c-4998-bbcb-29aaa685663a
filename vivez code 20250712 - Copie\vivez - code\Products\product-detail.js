function getCartItems() {
    return JSON.parse(localStorage.getItem('cart')) || [];
}

document.addEventListener('DOMContentLoaded', () => {
    const urlParams = new URLSearchParams(window.location.search);
    const productId = urlParams.get('id');
    
    const product = products.find(p => p.id === productId);
    
    // Populate main product info
    document.getElementById('mainProductImage').src = product.mainImage;
    document.getElementById('productName').textContent = product.name;
    document.getElementById('productPrice').textContent = `CHF ${product.price}`;
    document.getElementById('productDescription').textContent = product.description;
    
    // Create thumbnails
    const thumbnailsContainer = document.getElementById('productThumbnails');
    product.additionalImages.forEach((image, index) => {
        const thumbnail = document.createElement('img');
        thumbnail.src = image;
        thumbnail.alt = `${product.name} view ${index + 1}`;
        thumbnail.classList.add('thumbnail');
        if (index === 0) thumbnail.classList.add('active');
        
        thumbnail.addEventListener('click', () => {
            document.getElementById('mainProductImage').src = image;
            document.querySelectorAll('.thumbnail').forEach(thumb => thumb.classList.remove('active'));
            thumbnail.classList.add('active');
        });
        
        thumbnailsContainer.appendChild(thumbnail);
    });
    
    // Size selector functionality
    const sizeButtons = document.querySelectorAll('.size-btn');
    sizeButtons.forEach(button => {
        button.addEventListener('click', () => {
            sizeButtons.forEach(btn => btn.classList.remove('selected'));
            button.classList.add('selected');
        });
    });
    
    // New enhanced add to cart functionality
    const addToCartBtn = document.querySelector('.add-to-cart');
    addToCartBtn.addEventListener('click', () => {
        const selectedSize = document.querySelector('.size-btn.selected');
        if (!selectedSize) {
            alert('Please select a size');
            return;
        }
        
        const productId = new URLSearchParams(window.location.search).get('id');
        addToCart(productId, selectedSize.textContent);
        
        // Show success message
        addToCartBtn.textContent = 'Added to Cart!';
        addToCartBtn.style.background = 'var(--accent-color)';
        setTimeout(() => {
            addToCartBtn.textContent = 'Add to Cart';
            addToCartBtn.style.background = 'var(--primary-color)';
        }, 2000);
    });

    // Add event listener for the "Start Shopping" button with debugging
    document.querySelectorAll('.start-shopping-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            console.log('Start Shopping button clicked'); // Debugging log
            e.preventDefault();
            const cartItems = getCartItems(); // Assume this function checks the cart state
            if (cartItems.length === 0) {
                window.location.href = '/shop/shop.html';
            } else {
                document.querySelector('.cart-panel').classList.remove('active');
                document.querySelector('.cart-overlay').classList.remove('active');
            }
        });
    });

});
