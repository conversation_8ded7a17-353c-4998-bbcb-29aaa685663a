window.addEventListener("load", () => {
    const videos = document.querySelectorAll(".thevideo");
    const brandTitle = document.querySelector(".brand-title");
    const isMobile = window.innerWidth <= 768;

    const observerOptions = {
        root: null,
        rootMargin: '0px',
        threshold: 0.1
    };

    if (isMobile) {
        videos.forEach(video => {
            video.play();
            video.loop = true;
            video.muted = true;
            video.classList.add('video-dim');
        });
    } else {
        const videoObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const video = entry.target;
                    video.play().catch(() => {
                        console.warn('Video playback error');
                    });
                    video.classList.add('video-dim');
                } else {
                    entry.target.pause();
                }
            });
        }, observerOptions);

        videos.forEach(video => {
            videoObserver.observe(video);

            video.addEventListener("mouseover", () => {
                video.classList.remove('video-dim');
                video.play().catch(() => console.warn('Video playback error'));

                videos.forEach(otherVideo => {
                    if (otherVideo !== video) {
                        otherVideo.classList.add('video-dim');
                        otherVideo.pause();
                    }
                });
            });

            video.addEventListener("mouseout", (e) => {
                if (!e.relatedTarget?.classList.contains('discover-btn')) {
                    videos.forEach(v => {
                        v.classList.add('video-dim');
                        v.play().catch(() => console.warn('Video playback error'));
                    });
                }
            });
        });
    }

    if (brandTitle) {
        brandTitle.style.pointerEvents = 'none';
    }
});

const footer = document.querySelector('.footer');
if (footer) {
    const footerObserver = new IntersectionObserver(
        entries => {
            if (entries[0].isIntersecting) {
                entries[0].target.style.opacity = '1';
            }
        },
        { threshold: 0.1 }
    );
    footerObserver.observe(footer);
}


document.addEventListener('DOMContentLoaded', function() {
    const fadeInElements = document.querySelectorAll('.fade-in');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
                observer.unobserve(entry.target);
            }
        });
    });

    fadeInElements.forEach(element => observer.observe(element));
    fadeInElements.forEach(element => element.classList.remove('visible'));

    // Get existing elements
    const hamburger = document.querySelector('.hamburger-icon');
    const menuItems = document.querySelector('.menu-items');
    const menuOverlay = document.querySelector('.menu-overlay');
    const contentWrapper = document.querySelector('.content-wrapper');
    const cartPanel = document.querySelector('.cart-panel');
    const cartOverlay = document.querySelector('.cart-overlay');
    
    // Toggle menu function
    if (hamburger) {
        hamburger.addEventListener('click', function() {
            menuItems.classList.toggle('active');
            menuOverlay.classList.toggle('active');
            
            if (menuItems.classList.contains('active')) {
                contentWrapper.classList.add('blurred');
                document.body.style.overflow = 'hidden';
                
                // Close cart if it's open
                if (cartPanel) cartPanel.classList.remove('active');
                if (cartOverlay) cartOverlay.classList.remove('active');
            } else {
                contentWrapper.classList.remove('blurred');
                document.body.style.overflow = '';
            }
        });
    }
    
    // Close menu when clicking overlay
    if (menuOverlay) {
        menuOverlay.addEventListener('click', function() {
            menuItems.classList.remove('active');
            menuOverlay.classList.remove('active');
            contentWrapper.classList.remove('blurred');
            document.body.style.overflow = '';
        });
    }
    
    // Reset scroll position to the top on page load
    window.scrollTo(0, 0);
});

function handlePageTransition(targetPage) {
    const nextPage = document.createElement('iframe');
    nextPage.className = 'next-page-transition';

    nextPage.style.backgroundColor = 'white';
    nextPage.style.opacity = '0';
    document.body.appendChild(nextPage);

    nextPage.onload = () => {
        nextPage.style.opacity = '1';
        requestAnimationFrame(() => {
            nextPage.classList.add('slide-in');
        });

        setTimeout(() => {
            window.location.href = targetPage;
        }, 500);
    };

    nextPage.src = targetPage;
}

document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('.page-transition-link').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            handlePageTransition(e.target.closest('a').href);
        });
    });
});

// Cart functions
function getCartItems() {
    return JSON.parse(localStorage.getItem('cart')) || [];
}

document.querySelectorAll('.start-shopping-btn').forEach(button => {
    button.addEventListener('click', (e) => {
        e.preventDefault();
        const cartItems = getCartItems();

        if (cartItems.length === 0) {
            console.log("Cart is empty, redirecting to shop.html");
            window.location.href = '/shop/shop.html';
        } else {
            console.log("Cart has items, closing cart panel");
            document.querySelector('.cart-panel').classList.remove('active');
            document.querySelector('.cart-overlay').classList.remove('active');
        }
    });
});

function handleBackTransition(targetPage) {
    const transition = document.createElement('div');
    transition.className = 'next-page-transition';
    document.body.appendChild(transition);

    requestAnimationFrame(() => {
        transition.classList.add('slide-back');
    });

    setTimeout(() => {
        window.location.href = targetPage;
    }, 400);
}

const navCenterLink = document.querySelector('.nav-center a');
if (navCenterLink) {
    navCenterLink.addEventListener('click', function(e) {
        e.preventDefault();
        handleBackTransition('/');
    });
}


// Products - This section seems to be for a different page setup, but is preserved
document.addEventListener('DOMContentLoaded', function() {
    const productGrid = document.getElementById('productGrid');
    // Ensure this code doesn't run if productGrid doesn't exist on the page
    if (productGrid && typeof products !== 'undefined') {
        // Generate product cards
        products.forEach(product => {
            const productCard = `
                <div class="product-card" data-product-id="${product.id}">
                    <div class="product-image-container">
                        <img src="${product.image}" alt="${product.name}" class="product-image">
                    </div>
                    <h3 class="product-name">${product.name}</h3>
                    <p class="product-price">CHF ${product.price}</p>
                </div>
            `;
            productGrid.innerHTML += productCard;
        });

        // Add click event for product details
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const productId = card.dataset.productId;
                const product = products.find(p => p.id === productId);
                if (product) {
                    showProductDetails(product);
                }
            });
        });
    }
});


function showProductDetails(product) {
    const detailsHTML = `
        <div class="product-details">
            <img src="${product.image}" alt="${product.name}">
            <h2>${product.name}</h2>
            <p>${product.description}</p>
            <p class="price">CHF ${product.price}</p>
            <button class="add-to-cart">Add to Cart</button>
        </div>
    `;

    // Create modal or update page content
    const modal = document.createElement('div');
    modal.className = 'product-modal';
    modal.innerHTML = detailsHTML;
    document.body.appendChild(modal);
}