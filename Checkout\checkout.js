document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Checkout page loaded');

    const cart = JSON.parse(localStorage.getItem('cart')) || [];

    const orderItemsContainer = document.getElementById('order-items');
    const summarySubtotalElement = document.getElementById('summary-subtotal');
    const summaryTotalElement = document.getElementById('summary-total');

    function displayOrderSummary() {
        if (!orderItemsContainer) {
            console.error("Error: The '#order-items' container was not found.");
            return;
        }

        orderItemsContainer.innerHTML = '';

        if (cart.length === 0) {
            orderItemsContainer.innerHTML = '<p>Your cart is empty.</p>';
            if(summarySubtotalElement) summarySubtotalElement.textContent = 'CHF 0.00';
            if(summaryTotalElement) summaryTotalElement.textContent = 'CHF 0.00';
            return;
        }

        let subtotal = 0;
        cart.forEach(item => {
            const itemTotal = item.price * item.quantity;
            subtotal += itemTotal;
            const itemHTML = `
                <div class="summary-item">
                    <div class="summary-item-image">
                        <img src="${item.image}" alt="${item.name}">
                        <span class="summary-item-quantity">${item.quantity}</span>
                    </div>
                    <div class="summary-item-details">
                        <p class="summary-item-name">${item.name}</p>
                        ${item.size ? `<p class="summary-item-variant">Size: ${item.size}</p>` : ''}
                    </div>
                    <p class="summary-item-price">CHF ${itemTotal.toFixed(2)}</p>
                </div>`;
            orderItemsContainer.innerHTML += itemHTML;
        });

        if(summarySubtotalElement) summarySubtotalElement.textContent = `CHF ${subtotal.toFixed(2)}`;
        if(summaryTotalElement) summaryTotalElement.textContent = `CHF ${subtotal.toFixed(2)}`;

        // Update article count
        updateArticleCount();
    }

    // Update article count in order summary
    function updateArticleCount() {
        const articleCountElement = document.getElementById('article-count');
        if (articleCountElement) {
            const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
            articleCountElement.textContent = totalItems;
        }
    }

    displayOrderSummary();

    // Handle form submission
    const checkoutForm = document.querySelector('.checkout-form');
    if (checkoutForm) {
        checkoutForm.addEventListener('submit', handleFormSubmission);
    }

    // Update total when delivery option changes
    const deliveryOptions = document.querySelectorAll('input[name="delivery"]');
    deliveryOptions.forEach(option => {
        option.addEventListener('change', updateOrderTotal);
    });

    // Setup payment methods
    setupPaymentMethods();

    // Setup country-based payment methods
    setupCountryBasedPayments();

    // Setup card formatting
    setupCardFormatting();

    // Initialize PayPal with a small delay to ensure SDK is loaded
    setTimeout(() => {
        initializePayPal();
    }, 500);

    updateOrderTotal(); // Initial calculation
});

function updateOrderTotal() {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    let subtotal = 0;

    cart.forEach(item => {
        subtotal += item.price * item.quantity;
    });

    // Get delivery cost
    const selectedDelivery = document.querySelector('input[name="delivery"]:checked');
    const deliveryCost = selectedDelivery && selectedDelivery.value === 'express' ? 9.90 : 0;

    const total = subtotal + deliveryCost;

    // Update display
    const summarySubtotalElement = document.querySelector('#summary-subtotal');
    const summaryTotalElement = document.querySelector('#summary-total');
    const shippingElement = document.querySelector('#shipping-cost');

    if(summarySubtotalElement) summarySubtotalElement.textContent = `CHF ${subtotal.toFixed(2)}`;
    if(summaryTotalElement) summaryTotalElement.textContent = `CHF ${total.toFixed(2)}`;
    if(shippingElement) shippingElement.textContent = deliveryCost === 0 ? 'Free' : `CHF ${deliveryCost.toFixed(2)}`;
}

function handleFormSubmission(e) {
    e.preventDefault();

    // Validate required fields
    const requiredFields = [
        { selector: 'input[name="email"]', name: 'Email' },
        { selector: 'input[name="firstName"]', name: 'First name' },
        { selector: 'input[name="lastName"]', name: 'Last name' },
        { selector: 'input[name="address"]', name: 'Address' },
        { selector: 'input[name="city"]', name: 'City' },
        { selector: 'input[name="postalCode"]', name: 'Postal code' },
        { selector: 'select[name="country"]', name: 'Country' }
    ];

    let isValid = true;
    const errors = [];

    requiredFields.forEach(field => {
        const element = document.querySelector(field.selector);
        if (!element || !element.value.trim()) {
            isValid = false;
            errors.push(field.name);
            if (element) {
                element.style.borderColor = '#ff4444';
            }
        } else if (element) {
            element.style.borderColor = '';
        }
    });

    // Validate delivery option
    const deliverySelected = document.querySelector('input[name="delivery"]:checked');
    if (!deliverySelected) {
        isValid = false;
        errors.push('Delivery option');
    }

    // Check if payment method is selected
    const paymentSelected = document.querySelector('input[name="payment"]:checked');
    if (paymentSelected && paymentSelected.value === 'card') {
        // If credit card is selected, process payment directly
        if (!isValid) {
            alert(`Please fill in the following required fields:\n• ${errors.join('\n• ')}`);
            return;
        }

        // Save checkout data first
        saveCheckoutData();

        // Process credit card payment
        processCreditCardPayment();
        return;
    }

    if (!isValid) {
        alert(`Please fill in the following required fields:\n• ${errors.join('\n• ')}`);
        return;
    }

    // For other payment methods or no payment selected, redirect to payment page
    saveCheckoutData();
    window.location.href = 'payment.html';
}

function saveCheckoutData() {
    const formData = {
        email: document.querySelector('input[name="email"]').value,
        firstName: document.querySelector('input[name="firstName"]').value,
        lastName: document.querySelector('input[name="lastName"]').value,
        address: document.querySelector('input[name="address"]').value,
        apartment: document.querySelector('input[name="apartment"]').value,
        city: document.querySelector('input[name="city"]').value,
        postalCode: document.querySelector('input[name="postalCode"]').value,
        country: document.querySelector('select[name="country"]').value,
        delivery: document.querySelector('input[name="delivery"]:checked').value,
        timestamp: Date.now()
    };

    localStorage.setItem('checkoutData', JSON.stringify(formData));
    console.log('✅ Checkout data saved:', formData);
}

window.initializePayPal = function() {
    console.log('🔄 Initializing PayPal...');

    // Check if PayPal button container exists
    const container = document.getElementById('paypal-button-container');
    if (!container) {
        console.error('❌ PayPal container not found');
        return;
    }

    // Check if PayPal SDK is loaded
    if (typeof paypal === 'undefined') {
        console.error('❌ PayPal SDK not loaded');
        container.innerHTML = '<button class="express-btn paypal">PayPal (Loading...)</button>';
        return;
    }

    console.log('✅ PayPal SDK loaded successfully');

    // Get cart from localStorage
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    console.log('🛒 Cart contents:', cart);

    // Calculate totals
    let subtotal = 0;
    const items = [];

    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        subtotal += itemTotal;

        // Create PayPal item format
        items.push({
            name: item.name,
            unit_amount: {
                currency_code: 'CHF',
                value: item.price.toFixed(2)
            },
            quantity: item.quantity.toString(),
            description: item.size ? `Size: ${item.size}` : 'VIVEZ Product',
            sku: item.id || item.name.toLowerCase().replace(/\s+/g, '-')
        });
    });

    console.log('💰 Subtotal:', subtotal);
    console.log('📦 Items for PayPal:', items);

    // Only show PayPal if cart has items
    if (subtotal <= 0 || items.length === 0) {
        document.getElementById('paypal-button-container').innerHTML =
            '<button class="express-btn paypal" disabled>PayPal (Empty Cart)</button>';
        return;
    }

    // Render Advanced PayPal Button
    paypal.Buttons({
        style: {
            layout: 'horizontal',
            color: 'gold',
            shape: 'rect',
            label: 'paypal',
            height: 42,
            tagline: false
        },

        createOrder: function(data, actions) {
            console.log('🔄 Creating PayPal order with items:', items);

            return actions.order.create({
                purchase_units: [{
                    reference_id: 'VIVEZ_ORDER_' + Date.now(),
                    description: 'VIVEZ Store Purchase',
                    amount: {
                        currency_code: 'CHF',
                        value: subtotal.toFixed(2),
                        breakdown: {
                            item_total: {
                                currency_code: 'CHF',
                                value: subtotal.toFixed(2)
                            }
                        }
                    },
                    items: items
                }],
                application_context: {
                    brand_name: 'VIVEZ',
                    landing_page: 'BILLING',
                    user_action: 'PAY_NOW'
                }
            });
        },

        onApprove: function(data, actions) {
            console.log('✅ PayPal payment approved, Order ID:', data.orderID);

            return actions.order.capture().then(function(details) {
                console.log('✅ Payment captured:', details);

                // Show success message with order details
                const orderSummary = cart.map(item =>
                    `${item.quantity}x ${item.name}${item.size ? ` (${item.size})` : ''}`
                ).join('\n');

                alert(`🎉 Payment Successful!\n\nOrder ID: ${data.orderID}\nTotal: CHF ${subtotal.toFixed(2)}\n\nItems:\n${orderSummary}\n\nThank you for your purchase!`);

                // Clear cart and redirect
                localStorage.removeItem('cart');
                window.location.href = '/shop/shop.html';
            });
        },

        onError: function(err) {
            console.error('❌ PayPal Error:', err);
            alert('❌ Payment failed. Please try again or contact support.');
        },

        onCancel: function() {
            console.log('⚠️ Payment cancelled by user');
            alert('Payment was cancelled. Your cart is still saved.');
        }

    }).render('#paypal-button-container').then(() => {
        console.log('✅ PayPal button rendered successfully');

        // Force button size after render and add hover effects
        setTimeout(() => {
            const paypalContainer = document.getElementById('paypal-button-container');
            if (paypalContainer) {
                const allElements = paypalContainer.querySelectorAll('*');
                allElements.forEach(el => {
                    el.style.height = '42px';
                    el.style.maxHeight = '42px';
                    el.style.minHeight = '42px';
                });

                console.log('✅ PayPal button styling applied');
            }
        }, 100);

    }).catch(function(err) {
        console.error('❌ Failed to render PayPal button:', err);
        console.error('Error details:', err);
        const container = document.getElementById('paypal-button-container');
        if (container) {
            container.innerHTML = '<button class="express-btn paypal" onclick="initializePayPal()">PayPal (Click to Retry)</button>';
        }
    });
}

// Handle payment method selection
function setupPaymentMethods() {
    const paymentRadios = document.querySelectorAll('input[name="payment"]');
    const cardForm = document.getElementById('card-form');

    paymentRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            // Remove selected class from all payment methods
            document.querySelectorAll('.payment-method').forEach(method => {
                method.classList.remove('is-selected');
            });

            // Add selected class to current payment method
            this.closest('.payment-method').classList.add('is-selected');

            // Show/hide card form
            if (this.value === 'card') {
                cardForm.style.display = 'block';
            } else {
                cardForm.style.display = 'none';
            }
        });
    });

    // Set default selection
    const defaultPayment = document.querySelector('input[name="payment"]:checked');
    if (defaultPayment) {
        defaultPayment.closest('.payment-method').classList.add('is-selected');
        if (defaultPayment.value === 'card' && cardForm) {
            cardForm.style.display = 'block';
        }
    }
}

// Handle credit card payment
function processCreditCardPayment() {
    const formData = new FormData(document.getElementById('checkout-form'));
    const cart = JSON.parse(localStorage.getItem('cart')) || [];

    // Validate card fields
    const cardFields = ['cardNumber', 'expiry', 'cvv', 'cardName'];
    const missingCardFields = cardFields.filter(field => !formData.get(field));

    if (missingCardFields.length > 0) {
        alert('Please fill in all card details: ' + missingCardFields.join(', '));
        return;
    }

    // Simulate payment processing
    const submitBtn = document.querySelector('.continue-btn');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Processing...';
    submitBtn.disabled = true;

    setTimeout(() => {
        const orderData = {
            customer: {
                email: formData.get('email'),
                firstName: formData.get('firstName'),
                lastName: formData.get('lastName'),
                address: formData.get('address'),
                apartment: formData.get('apartment'),
                postalCode: formData.get('postalCode'),
                city: formData.get('city'),
                country: formData.get('country'),
                delivery: formData.get('delivery')
            },
            cart: cart,
            paymentMethod: 'credit_card',
            paymentDetails: {
                cardLast4: formData.get('cardNumber').slice(-4),
                cardName: formData.get('cardName')
            },
            orderDate: new Date().toISOString(),
            orderNumber: 'VZ' + Date.now()
        };

        // Save order
        localStorage.setItem('lastOrder', JSON.stringify(orderData));
        localStorage.removeItem('cart');

        // Show success
        const orderSummary = cart.map(item =>
            `${item.quantity}x ${item.name}${item.size ? ` (${item.size})` : ''}`
        ).join('\n');

        const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        alert(`🎉 Payment Successful!\n\nOrder Number: ${orderData.orderNumber}\nTotal: CHF ${total.toFixed(2)}\n\nItems:\n${orderSummary}\n\nThank you for your purchase!`);

        // Redirect to shop
        window.location.href = '/shop/shop.html';

    }, 2000);
}

// Setup country-based payment methods
function setupCountryBasedPayments() {
    const countrySelect = document.querySelector('select[name="country"]');
    const twintMethod = document.getElementById('twint-method');
    const postfinanceMethod = document.getElementById('postfinance-method');

    // EU countries + Switzerland
    const euCountries = [
        'Austria', 'Belgium', 'Bulgaria', 'Croatia', 'Cyprus', 'Czech Republic',
        'Denmark', 'Estonia', 'Finland', 'France', 'Germany', 'Greece',
        'Hungary', 'Ireland', 'Italy', 'Latvia', 'Lithuania', 'Luxembourg',
        'Malta', 'Netherlands', 'Poland', 'Portugal', 'Romania', 'Slovakia',
        'Slovenia', 'Spain', 'Sweden', 'Switzerland'
    ];

    // Currency mapping
    const currencyMap = {
        'Switzerland': 'CHF',
        'Germany': 'EUR', 'France': 'EUR', 'Italy': 'EUR', 'Spain': 'EUR',
        'Netherlands': 'EUR', 'Belgium': 'EUR', 'Austria': 'EUR', 'Portugal': 'EUR',
        'Finland': 'EUR', 'Ireland': 'EUR', 'Luxembourg': 'EUR', 'Slovenia': 'EUR',
        'Slovakia': 'EUR', 'Estonia': 'EUR', 'Latvia': 'EUR', 'Lithuania': 'EUR',
        'Malta': 'EUR', 'Cyprus': 'EUR', 'Greece': 'EUR',
        'Poland': 'PLN', 'Czech Republic': 'CZK', 'Hungary': 'HUF',
        'Bulgaria': 'BGN', 'Romania': 'RON', 'Croatia': 'EUR',
        'Denmark': 'DKK', 'Sweden': 'SEK'
    };

    if (countrySelect) {
        // Populate country dropdown with EU countries + Switzerland
        countrySelect.innerHTML = '<option value="">Select country</option>';
        euCountries.forEach(country => {
            const option = document.createElement('option');
            option.value = country;
            option.textContent = country;
            countrySelect.appendChild(option);
        });

        function updatePaymentMethods() {
            const selectedCountry = countrySelect.value;

            // Show/hide TWINT and PostFinance for Switzerland
            if (selectedCountry === 'Switzerland') {
                if (twintMethod) twintMethod.style.display = 'block';
                if (postfinanceMethod) postfinanceMethod.style.display = 'block';
            } else {
                if (twintMethod) twintMethod.style.display = 'none';
                if (postfinanceMethod) postfinanceMethod.style.display = 'none';

                // Uncheck Swiss payment methods if they were selected
                const twintRadio = twintMethod?.querySelector('input[type="radio"]');
                const postfinanceRadio = postfinanceMethod?.querySelector('input[type="radio"]');

                if (twintRadio?.checked || postfinanceRadio?.checked) {
                    const cardRadio = document.querySelector('input[name="payment"][value="card"]');
                    if (cardRadio) cardRadio.checked = true;
                }
            }

            // Update currency display
            updateCurrency(selectedCountry);
        }

        countrySelect.addEventListener('change', updatePaymentMethods);
        updatePaymentMethods(); // Initial check
    }
}

// Update currency display based on selected country
function updateCurrency(country) {
    const currencyMap = {
        'Switzerland': 'CHF',
        'Germany': 'EUR', 'France': 'EUR', 'Italy': 'EUR', 'Spain': 'EUR',
        'Netherlands': 'EUR', 'Belgium': 'EUR', 'Austria': 'EUR', 'Portugal': 'EUR',
        'Finland': 'EUR', 'Ireland': 'EUR', 'Luxembourg': 'EUR', 'Slovenia': 'EUR',
        'Slovakia': 'EUR', 'Estonia': 'EUR', 'Latvia': 'EUR', 'Lithuania': 'EUR',
        'Malta': 'EUR', 'Cyprus': 'EUR', 'Greece': 'EUR',
        'Poland': 'PLN', 'Czech Republic': 'CZK', 'Hungary': 'HUF',
        'Bulgaria': 'BGN', 'Romania': 'RON', 'Croatia': 'EUR',
        'Denmark': 'DKK', 'Sweden': 'SEK'
    };

    const currency = currencyMap[country] || 'EUR';

    // Update all price displays
    const priceElements = document.querySelectorAll('.price, .total-price, .summary-price');
    priceElements.forEach(element => {
        const text = element.textContent;
        const priceMatch = text.match(/[\d.,]+/);
        if (priceMatch) {
            const price = priceMatch[0];
            element.textContent = text.replace(/[A-Z]{3}\s*[\d.,]+|[\d.,]+\s*[A-Z]{3}/, `${currency} ${price}`);
        }
    });
}

// Setup card number formatting
function setupCardFormatting() {
    const cardNumberInput = document.querySelector('input[name="cardNumber"]');
    const expiryInput = document.querySelector('input[name="expiry"]');
    const cvvInput = document.querySelector('input[name="cvv"]');

    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            if (formattedValue !== e.target.value) {
                e.target.value = formattedValue;
            }
        });
    }

    if (expiryInput) {
        expiryInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + ' / ' + value.substring(2, 4);
            }
            e.target.value = value;
        });
    }

    if (cvvInput) {
        cvvInput.addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });
    }
}

// Handle express credit card button
document.addEventListener('click', function(e) {
    if (e.target.id === 'express-credit-card' || e.target.closest('#express-credit-card')) {
        e.preventDefault();

        const button = document.getElementById('express-credit-card');

        // Add loading animation
        button.classList.add('loading');

        // Remove loading after animation
        setTimeout(() => {
            button.classList.remove('loading');
        }, 800);

        // Scroll to payment section
        const paymentSection = document.querySelector('.payment-methods');
        if (paymentSection) {
            paymentSection.scrollIntoView({ behavior: 'smooth' });

            // Select credit card option
            const cardRadio = document.querySelector('input[name="payment"][value="card"]');
            if (cardRadio) {
                cardRadio.checked = true;
                cardRadio.dispatchEvent(new Event('change'));
            }
        }
    }


});

// Modal functions (global scope for onclick handlers)
function showShippingInfo() {
    const modal = document.getElementById('shipping-info-modal');
    if (modal) {
        modal.classList.add('show');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }
}

function hideShippingInfo() {
    const modal = document.getElementById('shipping-info-modal');
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = ''; // Restore scrolling
    }
}

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        hideShippingInfo();
    }
});

// Toggle delivery forms based on delivery method selection
function toggleDeliveryForms() {
    const homeDelivery = document.querySelector('input[name="delivery-type"][value="home"]');
    const pickupDelivery = document.querySelector('input[name="delivery-type"][value="pickup"]');
    const homeSection = document.getElementById('home-delivery-section');
    const pickupSection = document.getElementById('pickup-point-section');

    if (homeDelivery && homeDelivery.checked) {
        if (homeSection) homeSection.style.display = 'block';
        if (pickupSection) pickupSection.style.display = 'none';
    } else if (pickupDelivery && pickupDelivery.checked) {
        if (homeSection) homeSection.style.display = 'none';
        if (pickupSection) pickupSection.style.display = 'block';
    }
}

// Search for pickup points (placeholder function)
function searchPickupPoints() {
    const searchInput = document.getElementById('pickup-search');
    const pickupList = document.getElementById('pickup-points-list');

    if (!searchInput || !pickupList) return;

    const searchValue = searchInput.value.trim();
    if (!searchValue) {
        alert('Please enter a postal code or city');
        return;
    }

    // Placeholder pickup points (in real implementation, this would call an API)
    const mockPickupPoints = [
        {
            name: 'Coop Pronto',
            address: 'Bahnhofstrasse 12, 8001 Zürich',
            hours: 'Mon-Sun: 06:00-22:00',
            distance: '0.2 km'
        },
        {
            name: 'Migros',
            address: 'Limmatquai 94, 8001 Zürich',
            hours: 'Mon-Sat: 07:00-20:00',
            distance: '0.5 km'
        },
        {
            name: 'Post Office',
            address: 'Sihlpost, Kasernenstrasse 95, 8004 Zürich',
            hours: 'Mon-Fri: 08:00-18:30, Sat: 08:00-16:00',
            distance: '1.2 km'
        }
    ];

    pickupList.innerHTML = `
        <h4 style="margin-bottom: 1rem; color: #111827;">Available pickup points near "${searchValue}":</h4>
        ${mockPickupPoints.map((point, index) => `
            <label class="pickup-point-option" style="display: block; padding: 1rem; border: 1px solid #e5e7eb; border-radius: 6px; margin-bottom: 0.5rem; cursor: pointer;">
                <input type="radio" name="pickup-point" value="${index}" style="margin-right: 0.75rem;">
                <div>
                    <div style="font-weight: 600; color: #111827; margin-bottom: 0.25rem;">${point.name}</div>
                    <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.25rem;">${point.address}</div>
                    <div style="font-size: 0.875rem; color: #6b7280;">
                        <span>${point.hours}</span> • <span>${point.distance}</span>
                    </div>
                </div>
            </label>
        `).join('')}
    `;
}